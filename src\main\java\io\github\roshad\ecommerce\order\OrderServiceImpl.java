package io.github.roshad.ecommerce.order;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class OrderServiceImpl implements OrderService {
    private final OrderMapper orderMapper;
    private final io.github.roshad.ecommerce.auth.UserService userService;

    @Override
    public Order createOrder(Order order) {
        order.setStatus(OrderStatus.PENDING);
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        orderMapper.insert(order);
        return order;
    }

    @Override
    public Order getOrderById(Long id) {
        return orderMapper.findById(id);
    }

    @Override
    public List<Order> getOrdersByUserId(Long userId) {
        return orderMapper.findByUserId(userId);
    }

    @Override
    public void updateOrderStatus(Long orderId, OrderStatus status) {
        Order order = orderMapper.findById(orderId);
        if (order != null) {
            order.setStatus(status);
            order.setUpdatedAt(LocalDateTime.now());
            orderMapper.update(order);
        }
    }

    @Override
    public void cancelOrder(Long orderId) {
        updateOrderStatus(orderId, OrderStatus.CANCELLED);
    }
    @Override
    public boolean isOrderOwner(Long orderId, String username) {
        Order order = orderMapper.findById(orderId);
        if (order == null) {
            return false;
        }
        Long userId = userService.getUserId(username);
        return order.getUserId().equals(userId);
    }

    @Override
    public List<Order> getAllOrders() {
        return orderMapper.findAll();
    }
}