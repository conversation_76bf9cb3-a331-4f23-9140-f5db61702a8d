package io.github.roshad.ecommerce.order;

import java.util.List;

public interface OrderService {
    Order createOrder(Order order);
    Order getOrderById(Long id);
    List<Order> getOrdersByUserId(Long userId);
    void updateOrderStatus(Long orderId, OrderStatus status);
    void cancelOrder(Long orderId);
    boolean isOrderOwner(Long orderId, String username);
    List<Order> getAllOrders();
}