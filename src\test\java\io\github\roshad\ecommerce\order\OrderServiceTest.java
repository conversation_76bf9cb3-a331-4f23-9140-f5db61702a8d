package io.github.roshad.ecommerce.order;

import io.github.roshad.ecommerce.order.Order;
import io.github.roshad.ecommerce.order.OrderMapper;
import io.github.roshad.ecommerce.order.OrderServiceImpl;
import io.github.roshad.ecommerce.order.OrderStatus;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.doAnswer;

@ExtendWith(MockitoExtension.class)
class OrderServiceTest {
    @Mock
    private OrderMapper orderMapper;
    
    @InjectMocks
    private OrderServiceImpl orderService;

    @Test
    void createOrder_Success() {
        Order order = new Order();
        doAnswer(inv -> {
            Order o = inv.getArgument(0);
            o.setId(1L);
            return null;
        }).when(orderMapper).insert(any(Order.class));
        
        Order result = orderService.createOrder(order);
        
        assertNotNull(result.getId());
        assertEquals(OrderStatus.PENDING, result.getStatus());
        assertNotNull(result.getCreatedAt());
    }
    
    // 其他服务方法测试
}