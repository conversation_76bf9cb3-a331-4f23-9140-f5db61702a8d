package io.github.roshad.ecommerce.product;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProductService {

    @Autowired
    private ProductMapper productMapper;

    public List<Product> getAllProducts() {
        return productMapper.findAllProducts();
    }
    public Product getProductById(Long id) {
        return productMapper.findProductById(id);
    }

    public int createProduct(Product product) {
        return productMapper.insertProduct(product);
    }

    public int updateProduct(Product product) {
        return productMapper.updateProduct(product);
    }

    public int deleteProduct(Long id) {
        return productMapper.deleteProduct(id);
    }

    public List<Product> searchProducts(String keyword) {
        return productMapper.searchProducts(keyword);
    }
}