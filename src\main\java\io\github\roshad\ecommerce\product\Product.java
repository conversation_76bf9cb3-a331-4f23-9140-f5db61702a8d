package io.github.roshad.ecommerce.product;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class Product {
    private Long id; // AUTO_INCREMENT PRIMARY KEY
    @NotBlank // VARCHAR(255) NOT NULL
    private String name;
    private String description; // TEXT NULL
    @NotNull // DECIMAL(10, 2) NOT NULL
    private BigDecimal price;
    @NotNull // INT NOT NULL DEFAULT 0
    private Integer stock;
    private Integer categoryId; // INT NULL
    private String imageUrl; // VARCHAR(512) NULL
    private String tags; // JSON NULL
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}