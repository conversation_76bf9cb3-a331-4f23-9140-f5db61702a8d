package io.github.roshad.ecommerce.auth;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

@Service
public class AuthService implements UserDetailsService {

    private final PasswordEncoder passwordEncoder;
    private final UserMapper userMapper;

    @Autowired
    public AuthService(PasswordEncoder passwordEncoder, UserMapper userMapper) {
        this.passwordEncoder = passwordEncoder;
        this.userMapper = userMapper;
    }

    public String authenticate(String username, String password) {
        User user = userMapper.findByUsername(username);
        System.out.println("从数据库中检索到的用户: " + user);
        if (user == null) {
            return "认证失败：用户不存在";
        }
        if (passwordEncoder.matches(password, user.getPasswordHash())) {
            String token = JwtUtil.generateToken(username);
            return token;
        } else {
            return "认证失败：密码不正确";
        }
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userMapper.findByUsername(username); // Keep this for UserDetailsService
        if (user == null) {
            throw new UsernameNotFoundException("用户未找到: " + username);
        }
        return user;
    }
}
