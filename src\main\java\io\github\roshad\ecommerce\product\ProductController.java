package io.github.roshad.ecommerce.product;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/products")
public class ProductController {

    @Autowired
    private ProductService productService;

    @GetMapping
    public ResponseEntity<List<Product>> getAllProducts() {
        List<Product> products = productService.getAllProducts();
        return ResponseEntity.ok(products);
    }
    @GetMapping("/{id}")
    public ResponseEntity<Product> getProductById(@PathVariable Long id) {
        Product product = productService.getProductById(id);
        if (product != null) {
            return ResponseEntity.ok(product);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/search")
    public ResponseEntity<List<Product>> searchProducts(@RequestParam String keyword) {
        List<Product> products = productService.searchProducts(keyword);
        return ResponseEntity.ok(products);
    }

    @PostMapping
    public ResponseEntity<String> createProduct(@RequestBody Product product) {
        int result = productService.createProduct(product);
        if (result > 0) {
            return ResponseEntity.status(201).body("Product created successfully.");
        } else {
            return ResponseEntity.status(500).body("Failed to create product.");
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<String> updateProduct(@PathVariable Long id, @RequestBody Product product) {
        product.setId(id); // Ensure the ID from path is set to the product object
        int result = productService.updateProduct(product);
        if (result > 0) {
            return ResponseEntity.ok("Product updated successfully.");
        } else {
            return ResponseEntity.status(500).body("Failed to update product or product not found.");
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteProduct(@PathVariable Long id) {
        int result = productService.deleteProduct(id);
        if (result > 0) {
            return ResponseEntity.ok("Product deleted successfully.");
        } else {
            return ResponseEntity.status(500).body("Failed to delete product or product not found.");
        }
    }
}