package io.github.roshad.ecommerce.order;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface OrderMapper {
    @Insert("INSERT INTO orders (user_id, order_code, total_amount, status, " +
            "shipping_address_line1, shipping_address_line2, shipping_city, " +
            "shipping_postal_code, shipping_country, payment_method, payment_status, notes) " +
            "VALUES (#{userId}, #{orderCode}, #{totalAmount}, #{status}, " +
            "#{shippingAddressLine1}, #{shippingAddressLine2}, #{shippingCity}, " +
            "#{shippingPostalCode}, #{shippingCountry}, #{paymentMethod}, #{paymentStatus}, #{notes})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(Order order);

    @Select("SELECT * FROM orders WHERE id = #{id}")
    Order findById(Long id);

    @Update("UPDATE orders SET " +
            "user_id = #{userId}, order_code = #{orderCode}, total_amount = #{totalAmount}, " +
            "status = #{status}, shipping_address_line1 = #{shippingAddressLine1}, " +
            "shipping_address_line2 = #{shippingAddressLine2}, shipping_city = #{shippingCity}, " +
            "shipping_postal_code = #{shippingPostalCode}, shipping_country = #{shippingCountry}, " +
            "payment_method = #{paymentMethod}, payment_status = #{paymentStatus}, notes = #{notes} " +
            "WHERE id = #{id}")
    void update(Order order);

    @Delete("DELETE FROM orders WHERE id = #{id}")
    void delete(Long id);

    @Select("SELECT * FROM orders WHERE user_id = #{userId}")
    List<Order> findByUserId(Long userId);

    @Select("SELECT * FROM orders")
    List<Order> findAll();
}